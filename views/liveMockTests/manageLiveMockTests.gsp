<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    body {
        background: #f8f9fa !important;
    }
    main {
        min-height: 75vh;
        margin-top: 4rem;
    }
    .ws_container {
        width: calc(100% - 30%);
        margin: 0 auto;
    }
    .liveMockTests {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    .liveMockTests__title {
        font-size: 1.7rem;
        margin-bottom: 2rem;
        text-align: center;
        color: #333;
    }

    /* Modern Tab Navigation */
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
        display: flex;
        flex-wrap: wrap;
        gap: 0;
    }

    .nav-tabs li {
        margin-bottom: -2px;
        border: none;
    }

    .nav-tabs li a {
        border: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        color: #6c757d;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        background: #f8f9fa;
        margin-right: 4px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .nav-tabs li a:hover {
        background: #e9ecef;
        color: #495057;
        border: none;
    }

    .nav-tabs li.active a {
        background: #fff;
        color: #007bff;
        border: none;
        border-bottom: 2px solid #007bff;
        font-weight: 600;
    }

    .nav-tabs li a i {
        font-size: 16px;
    }

    .tab-pane {
        min-height: 400px;
    }

    /* Table Styles */
    .table-responsive {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 15px 12px;
        vertical-align: middle;
    }

    .table tbody td {
        padding: 12px;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Status badges */
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-badge.ongoing {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.upcoming {
        background: #fff3cd;
        color: #856404;
    }

    .status-badge.completed {
        background: #d1ecf1;
        color: #0c5460;
    }

    /* Delete button */
    .btn-delete {
        background: #dc3545;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .btn-delete:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
    }

    /* Loading and empty states */
    .loading-spinner {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .loading-spinner i {
        margin-bottom: 15px;
        color: #007bff;
    }

    .no-tests {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests i {
        margin-bottom: 15px;
    }

    .no-tests h4 {
        margin-bottom: 10px;
        color: #495057;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .ws_container {
            width: calc(100% - 4%);
        }
        .nav-tabs li a{
            padding: 12px 6px;
            gap: 5px;
        }
        .table-responsive {
            font-size: 14px;
        }
        .table thead th,
        .table tbody td {
            padding: 8px 6px;
        }
    }

    .tabpanel{
        padding: 0 !important;
    }
</style>

<main>
    <div class="ws_container">
        <section class="liveMockTests">
            <h1 class="liveMockTests__title">Manage Live Mock Tests</h1>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#ongoing-tests" aria-controls="ongoing-tests" role="tab" onclick="switchTab(event, 'ongoing')" data-tab="ongoing">
                        <i class="fa fa-play-circle"></i> On-Going
                    </a>
                </li>
                <li role="presentation">
                    <a href="#upcoming-tests" aria-controls="upcoming-tests" role="tab" onclick="switchTab(event, 'upcoming')" data-tab="upcoming">
                        <i class="fa-solid fa-hourglass-start"></i> Up-coming
                    </a>
                </li>
                <li role="presentation">
                    <a href="#completed-tests" aria-controls="completed-tests" role="tab" onclick="switchTab(event, 'completed')" data-tab="completed">
                        <i class="fa fa-check-circle"></i> Completed
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Ongoing Tests Tab -->
                <div role="tabpanel" class="tab-pane in active" id="ongoing-tests">
                    <div id="ongoing-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading ongoing tests...</p>
                        </div>
                    </div>
                    <div id="ongoing-tests-pagination"></div>
                </div>

                <!-- Upcoming Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="upcoming-tests">
                    <div id="upcoming-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading upcoming tests...</p>
                        </div>
                    </div>
                    <div id="upcoming-tests-pagination"></div>
                </div>

                <!-- Completed Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="completed-tests">
                    <div id="completed-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading completed tests...</p>
                        </div>
                    </div>
                    <div id="completed-tests-pagination"></div>
                </div>
            </div>
        </section>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var currentTab = 'ongoing';
    var currentPage = {
        ongoing: 1,
        upcoming: 1,
        completed: 1,
    };
    var pageSize = 10;

    // Initialize the page
    loadTests('ongoing', 1);

    // Function to switch tabs
    window.switchTab = function(event, tabType) {
        event.preventDefault();

        // Update tab navigation
        var tabs = document.querySelectorAll('.nav-tabs li');
        var tabPanes = document.querySelectorAll('.tab-pane');

        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });

        tabPanes.forEach(function(pane) {
            pane.classList.remove('active', 'in');
        });

        // Activate current tab
        event.target.closest('li').classList.add('active');
        document.getElementById(tabType + '-tests').classList.add('active', 'in');

        currentTab = tabType;
        loadTests(tabType, currentPage[tabType]);
    };

    // Function to load tests based on tab type and page
    async function loadTests(tabType, page) {
        if (!page) page = 1;

        var contentId = tabType + '-tests-content';
        var paginationId = tabType + '-tests-pagination';

        // Show loading spinner
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="loading-spinner">' +
            '<i class="fa fa-spinner fa-spin fa-2x"></i>' +
            '<p>Loading ' + tabType + ' tests...</p>' +
            '</div>';

        // Clear pagination
        document.getElementById(paginationId).innerHTML = '';

        var apiUrl;
        switch(tabType) {
            case 'ongoing':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getOngoingMockTests')}';
                break;
            case 'upcoming':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getUpcomingMockTests')}';
                break;
            case 'completed':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getCompletedMockTests')}';
                break;
        }

        try {
            // Use fetch with async/await
            const response = await fetch(apiUrl + '?max=' + pageSize + '&page=' + page, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }

            const data = await response.json();

            if (data.status === 'success') {
                renderTestsTable(data.data, contentId, tabType);
                renderPagination(data.pagination, paginationId, tabType);
                currentPage[tabType] = page;
            } else {
                showError(contentId, data.message || 'Failed to load tests');
            }
        } catch (error) {
            console.error('Error loading tests:', error);
            showError(contentId, 'Failed to load tests. Please try again.');
        }
    }

    // Function to render tests in table format
    function renderTestsTable(tests, contentId, tabType) {
        var contentElement = document.getElementById(contentId);

        if (!tests || tests.length === 0) {
            contentElement.innerHTML = '<div class="no-tests">' +
                '<i class="fa fa-inbox fa-3x" style="color: #dee2e6; margin-bottom: 15px;"></i>' +
                '<h4>No tests found</h4>' +
                '<p>There are no tests available in this category.</p>' +
                '</div>';
            return;
        }

        var html = '<div class="table-responsive">' +
            '<table class="table">' +
            '<thead>' +
            '<tr>' +
            '<th>Test Name</th>' +
            '<th>Questions</th>' +
            '<th>Duration</th>' +
            '<th>Start Date</th>' +
            '<th>End Date</th>' +
            '<th>Result Date</th>' +
            '<th>Status</th>' +
            '<th>Created By</th>' +
            '<th>Actions</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody>';

        for (var i = 0; i < tests.length; i++) {
            html += renderTestRow(tests[i], tabType);
        }

        html += '</tbody></table></div>';
        contentElement.innerHTML = html;
    }

    // Function to render individual test row
    function renderTestRow(test, tabType) {
        var startDate = test.testStartDate ? formatDate(new Date(test.testStartDate)) : 'Not set';
        var endDate = test.testEndDate ? formatDate(new Date(test.testEndDate)) : 'Not set';
        var resultDate = test.testResultDate ? formatDate(new Date(test.testResultDate)) : 'Not set';
        var createdDate = test.dateCreated ? formatDate(new Date(test.dateCreated)) : 'Not set';

        var statusClass = tabType;
        var statusText = tabType.charAt(0).toUpperCase() + tabType.slice(1);

        return '<tr>' +
            '<td><strong>' + (test.resourceName || 'Untitled Test') + '</strong></td>' +
            '<td>' + (test.mcqCount || 0) + '</td>' +
            '<td>' + (test.totalTime || 0) + ' mins</td>' +
            '<td>' + startDate + '</td>' +
            '<td>' + endDate + '</td>' +
            '<td>' + resultDate + '</td>' +
            '<td><span class="status-badge ' + statusClass + '">' + statusText + '</span></td>' +
            '<td>' + (test.createdBy || 'Unknown') + '</td>' +
            '<td>' +
                '<button class="btn-delete" onclick="deleteTest(' + test.id + ', \'' + (test.resourceName || 'this test') + '\')" ' +
                'title="Delete Test">' +
                '<i class="fa fa-trash"></i> Delete' +
                '</button>' +
            '</td>' +
            '</tr>';
    }

    // Function to render pagination
    function renderPagination(pagination, paginationId, tabType) {
        var paginationElement = document.getElementById(paginationId);

        if (!pagination || pagination.totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }

        var html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

        // Previous button
        if (pagination.currentPage > 1) {
            html += '<li class="page-item">' +
                '<a class="page-link" href="#" onclick="changePage(' + (pagination.currentPage - 1) + ', \'' + tabType + '\')">' +
                '<i class="fa fa-chevron-left"></i> Previous</a></li>';
        }

        // Page numbers
        var startPage = Math.max(1, pagination.currentPage - 2);
        var endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        if (startPage > 1) {
            html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1, \'' + tabType + '\')">1</a></li>';
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (var i = startPage; i <= endPage; i++) {
            var activeClass = (i === pagination.currentPage) ? ' active' : '';
            html += '<li class="page-item' + activeClass + '">' +
                '<a class="page-link" href="#" onclick="changePage(' + i + ', \'' + tabType + '\')">' + i + '</a></li>';
        }

        if (endPage < pagination.totalPages) {
            if (endPage < pagination.totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(' + pagination.totalPages + ', \'' + tabType + '\')">' + pagination.totalPages + '</a></li>';
        }

        // Next button
        if (pagination.currentPage < pagination.totalPages) {
            html += '<li class="page-item">' +
                '<a class="page-link" href="#" onclick="changePage(' + (pagination.currentPage + 1) + ', \'' + tabType + '\')">' +
                'Next <i class="fa fa-chevron-right"></i></a></li>';
        }

        html += '</ul></nav>';
        paginationElement.innerHTML = html;
    }

    // Handle pagination clicks
    window.changePage = function(page, tabType) {
        if (page && tabType) {
            loadTests(tabType, page);
        }
    };

    // Function to delete a test
    window.deleteTest = async function(mockTestId, testName) {
        if (!confirm('Are you sure you want to delete "' + testName + '"? This action cannot be undone.')) {
            return;
        }

        // Find and disable the delete button
        var deleteBtn = event.target.closest('button');
        var originalContent = deleteBtn.innerHTML;
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Deleting...';

        try {
            const response = await fetch('${createLink(controller: 'liveMockTests', action: 'deleteMockTest')}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'mockTestId=' + encodeURIComponent(mockTestId)
            });

            const data = await response.json();

            if (data.status === 'success') {
                alert('Test deleted successfully!');
                // Reload current tab to reflect changes
                loadTests(currentTab, currentPage[currentTab]);
            } else {
                alert('Error deleting test: ' + (data.message || 'Unknown error'));
                // Re-enable button on error
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = originalContent;
            }
        } catch (error) {
            console.error('Error deleting test:', error);
            alert('Error deleting test. Please try again.');
            // Re-enable button on error
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalContent;
        }
    };

    // Function to show error message
    function showError(contentId, message) {
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="no-tests">' +
            '<i class="fa fa-exclamation-triangle fa-3x" style="color: #dc3545; margin-bottom: 15px;"></i>' +
            '<h4>Error</h4>' +
            '<p>' + message + '</p>' +
            '<button class="btn btn-primary" onclick="loadTests(\'' + currentTab + '\', ' + currentPage[currentTab] + ')">' +
                '<i class="fa fa-refresh"></i> Retry' +
            '</button>' +
            '</div>';
    }

    // Utility function to format date
    function formatDate(date) {
        if (!date) return 'Not set';
        var options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('en-US', options);
    }
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>